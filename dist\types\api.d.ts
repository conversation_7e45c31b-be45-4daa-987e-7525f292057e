export interface ApiResponse<T = any> {
    success: boolean;
    message?: string;
    data?: T;
    error?: ApiErrorResponse;
    requestId?: string;
    timestamp?: string;
}
export interface ApiErrorResponse {
    code: string;
    message: string;
    details?: any;
    stack?: string;
}
export interface HealthCheckData {
    status: string;
    timestamp: string;
    uptime: number;
    memory: NodeJS.MemoryUsage;
    environment: string;
    version: string;
}
export interface EchoRequest {
    message?: string;
    data?: any;
}
export interface EchoResponse {
    received: {
        message?: string;
        data?: any;
        timestamp: string;
    };
    requestInfo: {
        method: string;
        path: string;
        requestId?: string;
        headers: {
            'content-type'?: string;
            'user-agent'?: string;
        };
    };
}
export interface ApiInfoData {
    name: string;
    version: string;
    description: string;
    endpoints: Record<string, string>;
    timestamp: string;
    requestId?: string;
}
export interface TestResponse {
    timestamp: string;
    requestId?: string;
    method: string;
    path: string;
}
export declare enum HttpStatusCode {
    OK = 200,
    CREATED = 201,
    NO_CONTENT = 204,
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    CONFLICT = 409,
    UNPROCESSABLE_ENTITY = 422,
    INTERNAL_SERVER_ERROR = 500,
    BAD_GATEWAY = 502,
    SERVICE_UNAVAILABLE = 503
}
export declare enum ErrorCode {
    VALIDATION_ERROR = "VALIDATION_ERROR",
    UNAUTHORIZED = "UNAUTHORIZED",
    FORBIDDEN = "FORBIDDEN",
    NOT_FOUND = "NOT_FOUND",
    CONFLICT = "CONFLICT",
    INTERNAL_ERROR = "INTERNAL_ERROR",
    ASYNC_ERROR = "ASYNC_ERROR",
    NETWORK_ERROR = "NETWORK_ERROR",
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
}
declare global {
    namespace Express {
        interface Request {
            id: string;
        }
    }
}
