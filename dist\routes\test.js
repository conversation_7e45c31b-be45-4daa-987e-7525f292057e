"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const errorHandler_1 = require("../middleware/errorHandler");
const errorHandler_2 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
require("../types/api");
const router = (0, express_1.Router)();
router.get('/', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    logger_1.logger.info('Test endpoint accessed', { requestId: req.id });
    res.json({
        success: true,
        message: 'API is working!',
        data: {
            timestamp: new Date().toISOString(),
            requestId: req.id,
            method: req.method,
            path: req.path
        }
    });
}));
router.get('/health', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const healthData = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0'
    };
    logger_1.logger.info('Health check accessed', { requestId: req.id, healthData });
    res.json({
        success: true,
        message: 'System is healthy',
        data: healthData
    });
}));
router.post('/echo', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { message, data } = req.body;
    logger_1.logger.info('Echo endpoint accessed', {
        requestId: req.id,
        body: req.body
    });
    res.json({
        success: true,
        message: 'Echo response',
        data: {
            received: {
                message,
                data,
                timestamp: new Date().toISOString()
            },
            requestInfo: {
                method: req.method,
                path: req.path,
                requestId: req.id,
                headers: {
                    'content-type': req.get('content-type'),
                    'user-agent': req.get('user-agent')
                }
            }
        }
    });
}));
router.get('/error', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { type } = req.query;
    logger_1.logger.info('Error test endpoint accessed', {
        requestId: req.id,
        errorType: type
    });
    switch (type) {
        case 'validation':
            throw new errorHandler_2.AppError('Validation error example', 400, 'VALIDATION_ERROR', {
                field: 'email',
                message: 'Email is required'
            });
        case 'unauthorized':
            throw new errorHandler_2.AppError('Unauthorized access', 401, 'UNAUTHORIZED');
        case 'forbidden':
            throw new errorHandler_2.AppError('Access forbidden', 403, 'FORBIDDEN');
        case 'notfound':
            throw new errorHandler_2.AppError('Resource not found', 404, 'NOT_FOUND');
        default:
            throw new errorHandler_2.AppError('Internal server error example', 500, 'INTERNAL_ERROR');
    }
}));
router.get('/async-error', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    logger_1.logger.info('Async error test endpoint accessed', { requestId: req.id });
    await new Promise((resolve, reject) => {
        setTimeout(() => {
            reject(new errorHandler_2.AppError('Async operation failed', 500, 'ASYNC_ERROR'));
        }, 100);
    });
}));
router.get('/info', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    res.json({
        success: true,
        message: 'API Information',
        data: {
            name: 'Zalo Chatbot API',
            version: '1.0.0',
            description: 'API for Zalo Chatbot with TypeScript',
            endpoints: {
                'GET /api/test': 'Basic test endpoint',
                'GET /api/test/health': 'Detailed health check',
                'POST /api/test/echo': 'Echo POST data',
                'GET /api/test/error?type=<type>': 'Test error handling (types: validation, unauthorized, forbidden, notfound)',
                'GET /api/test/async-error': 'Test async error handling',
                'GET /api/test/info': 'This endpoint'
            },
            timestamp: new Date().toISOString(),
            requestId: req.id
        }
    });
}));
exports.default = router;
//# sourceMappingURL=test.js.map