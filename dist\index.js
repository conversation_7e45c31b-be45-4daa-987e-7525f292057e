"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const ZaloBot_1 = require("./services/ZaloBot");
const config_1 = require("./config/config");
const logger_1 = require("./utils/logger");
const app_1 = __importDefault(require("./app"));
async function main() {
    try {
        logger_1.logger.info('🚀 Starting Zalo Chatbot with Express API...');
        const port = process.env.PORT || 3000;
        const expressApp = new app_1.default(Number(port));
        expressApp.listen();
        const bot = new ZaloBot_1.ZaloBot(config_1.config.zalo);
        await bot.initialize();
        logger_1.logger.info('✅ Zalo Chatbot and Express API started successfully!');
        logger_1.logger.info(`📍 API available at: http://localhost:${port}`);
        logger_1.logger.info(`📍 Test endpoints: http://localhost:${port}/api/test`);
    }
    catch (error) {
        logger_1.logger.error('❌ Failed to start application:', error);
        process.exit(1);
    }
}
process.on('SIGINT', () => {
    logger_1.logger.info('🛑 Shutting down gracefully...');
    process.exit(0);
});
process.on('SIGTERM', () => {
    logger_1.logger.info('🛑 Shutting down gracefully...');
    process.exit(0);
});
main().catch(error => {
    logger_1.logger.error('💥 Unhandled error:', error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map