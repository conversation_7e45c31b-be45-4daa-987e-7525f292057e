"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const logger_1 = require("./utils/logger");
require("./types/api");
const test_1 = __importDefault(require("./routes/test"));
const zalo_webhook_1 = __importDefault(require("./routes/zalo-webhook"));
const errorHandler_1 = require("./middleware/errorHandler");
const notFoundHandler_1 = require("./middleware/notFoundHandler");
class ExpressApp {
    app;
    port;
    constructor(port = 3000) {
        this.app = (0, express_1.default)();
        this.port = port;
        this.initializeMiddleware();
        this.initializeRoutes();
        this.initializeErrorHandling();
    }
    initializeMiddleware() {
        this.app.use((0, helmet_1.default)());
        this.app.use((0, cors_1.default)({
            origin: process.env.NODE_ENV === 'production'
                ? ['https://yourdomain.com']
                : ['http://localhost:3000', 'http://localhost:3001'],
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
        }));
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        if (process.env.NODE_ENV !== 'test') {
            this.app.use((0, morgan_1.default)('combined', {
                stream: {
                    write: (message) => {
                        logger_1.logger.info(message.trim());
                    }
                }
            }));
        }
        this.app.use((req, res, next) => {
            req.id = Math.random().toString(36).substring(2, 15);
            res.setHeader('X-Request-ID', req.id);
            next();
        });
        this.app.get('/health', (_req, res) => {
            res.status(200).json({
                status: 'OK',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                environment: process.env.NODE_ENV || 'development'
            });
        });
    }
    initializeRoutes() {
        this.app.use('/api/test', test_1.default);
        this.app.use('/api/zalo-webhook', zalo_webhook_1.default);
        this.app.get('/', (req, res) => {
            res.json({
                message: 'Zalo Chatbot API',
                version: '1.0.0',
                endpoints: {
                    health: '/health',
                    test: '/api/test',
                    zaloWebhook: '/api/zalo-webhook'
                }
            });
        });
    }
    initializeErrorHandling() {
        this.app.use(notFoundHandler_1.notFoundHandler);
        this.app.use(errorHandler_1.errorHandler);
    }
    listen() {
        this.app.listen(this.port, () => {
            logger_1.logger.info(`🚀 Express server is running on port ${this.port}`);
            logger_1.logger.info(`📍 Health check: http://localhost:${this.port}/health`);
            logger_1.logger.info(`📍 API docs: http://localhost:${this.port}/api/test`);
            logger_1.logger.info(`📍 Zalo Webhook: http://localhost:${this.port}/api/zalo-webhook`);
        });
    }
    getApp() {
        return this.app;
    }
}
exports.default = ExpressApp;
//# sourceMappingURL=app.js.map