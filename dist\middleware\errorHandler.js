"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.errorHandler = exports.AppError = void 0;
const logger_1 = require("../utils/logger");
require("../types/api");
class AppError extends Error {
    statusCode;
    code;
    details;
    constructor(message, statusCode = 500, code, details) {
        super(message);
        this.statusCode = statusCode;
        this.code = code || 'INTERNAL_ERROR';
        this.details = details;
        this.name = 'AppError';
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}
exports.AppError = AppError;
const errorHandler = (error, req, res, _next) => {
    const statusCode = error.statusCode || 500;
    const message = error.message || 'Internal Server Error';
    const code = error.code || 'INTERNAL_ERROR';
    logger_1.logger.error('API Error:', {
        requestId: req.id,
        method: req.method,
        url: req.url,
        statusCode,
        code,
        message,
        stack: error.stack,
        details: error.details
    });
    const isDevelopment = process.env.NODE_ENV !== 'production';
    const errorResponse = {
        success: false,
        error: {
            code,
            message,
            ...(isDevelopment && {
                stack: error.stack,
                details: error.details
            })
        },
        requestId: req.id,
        timestamp: new Date().toISOString()
    };
    res.status(statusCode).json(errorResponse);
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map