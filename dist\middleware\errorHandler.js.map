{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,4CAAyC;AACzC,wBAAsB;AAWtB,MAAa,QAAS,SAAQ,KAAK;IAC1B,UAAU,CAAS;IACnB,IAAI,CAAS;IACb,OAAO,CAAO;IAErB,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,IAAa,EAAE,OAAa;QACjF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,gBAAgB,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;QAGvB,IAAK,KAAa,CAAC,iBAAiB,EAAE,CAAC;YACpC,KAAa,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;CACF;AAjBD,4BAiBC;AAEM,MAAM,YAAY,GAAG,CAC1B,KAAe,EACf,GAAY,EACZ,GAAa,EACb,KAAmB,EACb,EAAE;IACR,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC;IAC3C,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,uBAAuB,CAAC;IACzD,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,IAAI,gBAAgB,CAAC;IAG5C,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE;QACzB,SAAS,EAAE,GAAG,CAAC,EAAE;QACjB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,UAAU;QACV,IAAI;QACJ,OAAO;QACP,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,OAAO,EAAE,KAAK,CAAC,OAAO;KACvB,CAAC,CAAC;IAGH,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC;IAE5D,MAAM,aAAa,GAAG;QACpB,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI;YACJ,OAAO;YACP,GAAG,CAAC,aAAa,IAAI;gBACnB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC;SACH;QACD,SAAS,EAAE,GAAG,CAAC,EAAE;QACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC;IAEF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC7C,CAAC,CAAC;AAxCW,QAAA,YAAY,gBAwCvB;AAGK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB"}